import {
	List,
	Create,
	Update,
	Delete,
	ToggleOnline,
	GetCourseSchedule
} from '@/store/small-class';

import { ItemResponse } from './types';

export const ListStore = new List<Array<ItemResponse>>({});
export const CreateStore = new Create({});
export const UpdateStore = new Update({});
export const DeleteStore = new Delete({});
export const ToggleOnlineStore = new ToggleOnline({});
export const GetCourseScheduleStore = new GetCourseSchedule<Array<ItemResponse>>({});
