import { getMapfromArray, getStorefromArray } from '@/shard/utils';
import { ColorEnum } from '@/shard/constant';

// 课程状态选项
export const COURSE_STATUS_OPTIONS = [
	{
		label: '报名中',
		value: 1,
		styleclass: ColorEnum.warning
	},
	{
		label: '进行中',
		value: 2,
		styleclass: ColorEnum.success
	},
	{
		label: '已结束',
		value: 3,
		styleclass: ColorEnum.info
	},
	{
		label: '已下线',
		value: 4,
		styleclass: ColorEnum.danger
	}
];
export const COURSE_STATUS_MAP = getMapfromArray(COURSE_STATUS_OPTIONS);
export const COURSE_STATUS_STORE = getStorefromArray(COURSE_STATUS_OPTIONS);

// 小班课专用车型选项（只有小车和货车）
export const SMALL_CLASS_CAR_TYPE_OPTIONS = [
	{
		label: '小车',
		value: 'car'
	},
	{
		label: '货车',
		value: 'truck'
	}
];
export const SMALL_CLASS_CAR_TYPE_MAP = getMapfromArray(SMALL_CLASS_CAR_TYPE_OPTIONS);
export const SMALL_CLASS_CAR_TYPE_STORE = getStorefromArray(SMALL_CLASS_CAR_TYPE_OPTIONS);

// 小班课专用科目选项（只有科一和科四）
export const SMALL_CLASS_KEMU_OPTIONS = [
	{
		label: '科一',
		value: 1
	},
	{
		label: '科四',
		value: 4
	}
];
export const SMALL_CLASS_KEMU_MAP = getMapfromArray(SMALL_CLASS_KEMU_OPTIONS);
export const SMALL_CLASS_KEMU_STORE = getStorefromArray(SMALL_CLASS_KEMU_OPTIONS);

// 上下线状态选项
export const ONLINE_STATUS_OPTIONS = [
	{
		label: '下线',
		value: false,
		styleclass: ColorEnum.danger
	},
	{
		label: '上线',
		value: true,
		styleclass: ColorEnum.success
	}
];
export const ONLINE_STATUS_MAP = getMapfromArray(ONLINE_STATUS_OPTIONS);
export const ONLINE_STATUS_STORE = getStorefromArray(ONLINE_STATUS_OPTIONS);
