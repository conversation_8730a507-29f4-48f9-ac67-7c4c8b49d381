<template>
	<pm-effi :controller="controller">
		<pm-search>
			<pm-search-single
				:span="4"
				:antdProps="{
					placeholder: '课程名称'
				}"
				data-index="courseName"
				xtype="INPUT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '车型'
				}"
				data-index="carType"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '科目'
				}"
				data-index="kemu"
				xtype="SELECT"
			/>
			<pm-search-single
				:span="3"
				:antdProps="{
					placeholder: '课程状态'
				}"
				data-index="status"
				xtype="SELECT"
			/>
		</pm-search>

		<pm-table>
			<template #toolbar>
				<m-button type="primary" @click="onCreate">添加课程</m-button>
			</template>

			<template #status="{ record }">
				<m-tag :color="getStatusColor(record)">
					{{ getStatusText(record) }}
				</m-tag>
			</template>

			<template #action="{ record }">
				<m-space>
					<m-button type="link" size="small" @click="onViewSchedule(record)">
						查看课程表
					</m-button>
					<m-button 
						type="link" 
						size="small" 
						:style="{ color: record.isOnline ? '#ff4d4f' : '#52c41a' }"
						@click="onToggleOnline(record)"
					>
						{{ record.isOnline ? '下架' : '上架' }}
					</m-button>
					<m-button type="link" size="small" @click="onEdit(record)">
						编辑
					</m-button>
				</m-space>
			</template>
		</pm-table>
	</pm-effi>

	<edit-dialog ref="editRef" @refresh="onRefresh" />
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';
import { ModelController, MESSAGE_TYPE, MUtils } from 'admin-library';
import { useRouter } from 'vue-router';

import { COLUMNS } from './config';
import { 
	COURSE_STATUS_OPTIONS, 
	SMALL_CLASS_CAR_TYPE_OPTIONS, 
	SMALL_CLASS_KEMU_OPTIONS,
	COURSE_STATUS_MAP
} from './constant';
import { ListStore, ToggleOnlineStore } from './store';
import { ItemResponse } from './types';
import EditDialog from './comps/edit-dialog/index.vue';

export default defineComponent({
	components: {
		EditDialog
	},
	setup() {
		const router = useRouter();
		
		const components = {
			editRef: ref<InstanceType<typeof EditDialog>>()
		};

		const constants = {
			COLUMNS
		};

		const state = reactive({});

		const controller = new ModelController({
			table: {
				store: ListStore
			},
			search: {
				carType: {
					store: { data: SMALL_CLASS_CAR_TYPE_OPTIONS }
				},
				kemu: {
					store: { data: SMALL_CLASS_KEMU_OPTIONS }
				},
				status: {
					store: { data: COURSE_STATUS_OPTIONS }
				}
			}
		});
		
		controller.tableRequest();

		const methods = {
			getStatusText(record: ItemResponse) {
				const now = new Date();
				const startDate = new Date(record.startDate + ' 00:00:00');
				const endDate = new Date(record.endDate + ' 23:59:59');

				if (!record.isOnline) {
					return '已下线';
				} else if (now < startDate) {
					return '报名中';
				} else if (now >= startDate && now <= endDate) {
					return '进行中';
				} else {
					return '已结束';
				}
			},
			
			getStatusColor(record: ItemResponse) {
				const status = methods.getStatusText(record);
				switch (status) {
					case '报名中': return 'orange';
					case '进行中': return 'green';
					case '已结束': return 'default';
					case '已下线': return 'red';
					default: return 'default';
				}
			},

			onCreate() {
				components.editRef.value?.open();
			},

			onEdit(record: ItemResponse) {
				components.editRef.value?.open(record);
			},

			async onToggleOnline(record: ItemResponse) {
				const action = record.isOnline ? '下架' : '上架';
				const confirmed = await MUtils.confirm(`确定要${action}该课程吗？`);

				if (!confirmed) return;

				try {
					await ToggleOnlineStore.request({
						id: record.id,
						isOnline: !record.isOnline
					}).getData();

					MUtils.toast('操作成功', MESSAGE_TYPE.success);
					controller.tableRequest();
				} catch (error) {
					console.error('Toggle online status failed:', error);
					MUtils.toast('操作失败，请重试', MESSAGE_TYPE.error);
				}
			},

			onViewSchedule(record: ItemResponse) {
				// 跳转到课程表页面，带上小班课ID作为筛选条件
				router.push({
					path: '/courses',
					query: {
						smallClassId: record.id
					}
				});
			},

			onRefresh() {
				controller.tableRequest();
			}
		};

		return {
			...toRefs(state),
			...components,
			...constants,
			...methods,
			controller
		};
	}
});
</script>
