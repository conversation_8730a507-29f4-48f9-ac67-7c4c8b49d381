import { createRouter, createWebHashHistory } from 'vue-router';

const routes = [
	{
		path: '/customer',
		name: 'customer',
		component: () => import(`@/application/customer/index.vue`)
	},
	{
		path: '/student',
		name: 'student',
		component: () => import(`@/application/student/index.vue`)
	},
	{
		path: '/promoter',
		name: 'promoter',
		component: () => import(`@/application/promoter/index.vue`)
	},
	{
		path: '/protocol',
		name: 'protocol',
		component: () => import(`@/application/protocol/index.vue`)
	},
	{
		path: '/wechat-protocol',
		name: 'wechat-protocol',
		component: () => import(`@/application/protocol/index.vue`)
	},
	{
		path: '/my-protocol',
		name: 'my-protocol',
		component: () => import(`@/application/my-protocol/index.vue`)
	},
	{
		path: '/bulk-message',
		name: 'bulk-message',
		component: () => import(`@/application/bulk-message/index.vue`)
	},
	{
		path: '/student-conversion-stat',
		name: 'student-conversion-stat',
		component: () => import(`@/application/student-conversion-stat/index.vue`)
	},
	{
		path: '/lesson',
		name: 'lesson',
		component: () => import(`@/application/lesson/index.vue`)
	},
	{
		path: '/part-time-teacher',
		name: 'part-time-teacher',
		component: () => import(`@/application/part-time-teacher/index.vue`)
	},
	{
		path: '/courses',
		name: 'courses',
		component: () => import(`@/application/courses/index.vue`)
	},
	{
		path: '/courses-schedule',
		name: 'courses-schedule',
		component: () => import(`@/application/courses-schedule/index.vue`)
	},
	{
		path: '/settlement',
		name: 'settlement',
		component: () => import(`@/application/settlement/index.vue`)
	},
	{
		path: '/teacher-level-manage',
		name: 'teacher-level-manage',
		component: () => import(`@/application/teacher-level-manage/index.vue`)
	},
	{
		path: '/lesson-group',
		name: 'lesson-group',
		component: () => import(`@/application/lesson-group/index.vue`)
	},
	{
		path: '/membership-student',
		name: 'membership-student',
		component: () => import(`@/application/student/index.vue`)
	},
	{
		path: '/recommend-lecturer',
		name: 'recommend-lecturer',
		component: () => import(`@/application/recommend-lecturer/index.vue`)
	},
	{
		path: '/lecturer-schedule',
		name: 'lecturer-schedule',
		component: () => import(`@/application/lecturer-schedule/index.vue`)
	},
	{
		path: '/bulk-delete',
		name: 'bulk-delete',
		component: () => import(`@/application/bulk-delete/index.vue`)
	},
	{
		path: '/group-automation',
		name: 'group-automation',
		component: () => import(`@/application/group-automation/index.vue`)
	},
	{
		path: '/group-automaiton-tmpl',
		name: 'group-automaiton-tmpl',
		component: () => import(`@/application/group-automaiton-tmpl/index.vue`)
	},
	{
		path: '/bulk-message-tmpl',
		name: 'bulk-message-tmpl',
		component: () => import(`@/application/bulk-message-tmpl/index.vue`)
	},
	{
		path: '/student-evaluate',
		name: 'student-evaluate',
		component: () => import(`@/application/student-evaluate/index.vue`)
	},
	{
		path: '/contact-records',
		name: 'contact-records',
		component: () => import(`@/application/contact-records/index.vue`)
	},
	{
		path: '/schedule-clear-record',
		name: 'schedule-clear-record',
		component: () => import(`@/application/schedule-clear-record/index.vue`)
	},
	{
		path: '/courseware',
		name: 'courseware',
		component: () => import(`@/application/courseware/index.vue`)
	},
	{
		path: '/teacher-data-bank',
		name: 'teacher-data-bank',
		component: () => import(`@/application/teacher-data-bank/index.vue`)
	},
	{
		path: '/bulk-message-daily',
		name: 'bulk-message-daily',
		component: () => import(`@/application/bulk-message-daily/index.vue`)
	},
	{
		path: '/course-monitor',
		name: 'course-monitor',
		component: () => import(`@/application/course-monitor/index.vue`)
	},
	{
		path: '/im-bulk-message',
		name: 'im-bulk-message',
		component: () => import(`@/application/im-bulk-message/index.vue`)
	},
	{
		path: '/im-group-automaiton-tmpl',
		name: 'im-group-automaiton-tmpl',
		component: () => import(`@/application/im-group-automaiton-tmpl/index.vue`)
	},
	{
		path: '/im-group-config',
		name: 'im-group-config',
		component: () => import(`@/application/im-group-config/index.vue`)
	},
	{
		path: '/course-invite',
		name: 'course-invite',
		component: () => import(`@/application/course-invite/index.vue`)
	},
	{
		path: '/invite-blacklist-lecture',
		name: 'invite-blacklist-lecture',
		component: () => import(`@/application/invite-blacklist-lecture/index.vue`)
	},
	{
		path: '/specialized',
		name: 'specialized',
		component: () => import(`@/application/specialized/index.vue`)
	},
	{
		path: '/lecturer-skill-groups',
		name: 'lecturer-skill-groups',
		component: () => import(`@/application/lecturer-skill-groups/index.vue`)
	},
	{
		path: '/flow-dispense-config',
		name: 'flow-dispense-config',
		component: () => import(`@/application/flow-dispense-config/index.vue`)
	},
	{
		path: '/leads',
		name: 'leads',
		component: () => import(`@/application/leads/index.vue`)
	},

	{
		path: '/real-exam-records',
		name: 'real-exam-records',
		component: () => import(`@/popup-pages/real-exam-records/index.vue`)
	},
	{
		path: '/mock-records',
		name: 'mock-records',
		component: () => import(`@/popup-pages/mock-records/index.vue`)
	},
	{
		path: '/wrong-questions',
		name: 'wrong-questions',
		component: () => import(`@/popup-pages/wrong-questions/index.vue`)
	},
	{
		path: '/homework',
		name: 'homework',
		component: () => import(`@/popup-pages/homework/index.vue`)
	},
	{
		path: '/course-task-record',
		name: 'course-task-record',
		component: () => import(`@/popup-pages/course-task-record/index.vue`)
	},
	{
		path: '/course-exercise',
		name: 'course-exercise',
		component: () => import(`@/popup-pages/course-exercise/index.vue`)
	},
	{
		path: '/exercise-wrong-book',
		name: 'exercise-wrong-book',
		component: () => import(`@/popup-pages/exercise-wrong-book/index.vue`)
	},
	{
		path: '/exercise-special',
		name: 'exercise-special',
		component: () => import(`@/popup-pages/exercise-special/index.vue`)
	},
	{
		path: '/exercise-mock-exam',
		name: 'exercise-mock-exam',
		component: () => import(`@/popup-pages/exercise-mock-exam/index.vue`)
	},
	{
		path: '/student-remark-records',
		name: 'student-remark-records',
		component: () => import(`@/popup-pages/student-remark-records/index.vue`)
	},
	{
		path: '/level-change-log',
		name: 'level-change-log',
		component: () => import(`@/popup-pages/level-change-log/index.vue`)
	},
	{
		path: '/skill-list',
		name: 'skill-list',
		component: () => import(`@/popup-pages/skill-list/index.vue`)
	},
	{
		path: '/train-progress',
		name: 'train-progress',
		component: () => import(`@/popup-pages/train-progress/index.vue`)
	},
	{
		path: '/inspect-list',
		name: 'inspect-list',
		component: () => import(`@/popup-pages/inspect-list/index.vue`)
	},
	{
		path: '/courseware-resource',
		name: 'courseware-resource',
		component: () => import(`@/popup-pages/courseware-resource/index.vue`)
	},
	{
		path: '/study-plan-change-records',
		name: 'study-plan-change-records',
		component: () => import(`@/popup-pages/study-plan-change-records/index.vue`)
	},
	{
		path: '/score-monitor',
		name: 'score-monitor',
		component: () => import(`@/popup-pages/score-monitor/index.vue`)
	},
	{
		path: '/course-test',
		name: 'course-test',
		component: () => import(`@/popup-pages/course-test/index.vue`)
	},
	{
		path: '/course-content',
		name: 'course-content',
		component: () => import(`@/popup-pages/course-content/index.vue`)
	},
	{
		path: '/group-control',
		name: 'group-control',
		component: () => import(`@/popup-pages/group-control/index.vue`)
	},
	{
		path: '/data-bank-progress',
		name: 'data-bank-progress',
		component: () => import(`@/popup-pages/data-bank-progress/index.vue`)
	},
	{
		path: '/room-detail',
		name: 'room-detail',
		component: () => import(`@/popup-pages/room-detail/index.vue`)
	},
	{
		path: '/course-invite-detail',
		name: 'course-invite-detail',
		component: () => import(`@/popup-pages/course-invite-detail/index.vue`)
	},
	{
		path: '/lecturer-invite-record',
		name: 'lecturer-invite-record',
		component: () => import(`@/popup-pages/lecturer-invite-record/index.vue`)
	},
	{
		path: '/layer-change-log',
		name: 'layer-change-log',
		component: () => import(`@/popup-pages/layer-change-log/index.vue`)
	},
	{
		path: '/specialized-question',
		name: 'specialized-question',
		component: () => import(`@/popup-pages/specialized-question/index.vue`)
	},
	{
		path: '/invite-black-record',
		name: 'invite-black-record',
		component: () => import(`@/popup-pages/invite-black-record/index.vue`)
	},
	{
		path: '/pt-account',
		name: 'pt-account',
		component: () => import(`@/application/pt-account/index.vue`)
	},
];
const router = createRouter({
	history: createWebHashHistory(),
	routes
});

export default router;
