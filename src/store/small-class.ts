import { AgentStore, MethodTypeModel } from 'admin-library';

export class List<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/list.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}

export class Create<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/create.htm`;
	method: MethodTypeModel = 'POST';
}

export class Update<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/update.htm`;
	method: MethodTypeModel = 'POST';
}

export class Delete<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/delete.htm`;
	method: MethodTypeModel = 'POST';
}

export class ToggleOnline<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/toggle-online.htm`;
	method: MethodTypeModel = 'POST';
}

export class GetCourseSchedule<T> extends AgentStore<T> {
	url = `${APP.domain['parrot']}/api/admin/small-class/course-schedule.htm`;
	method: MethodTypeModel = 'GET';
	options = {
		dataFilter(data) {
			return data?.itemList || data;
		}
	};
}
